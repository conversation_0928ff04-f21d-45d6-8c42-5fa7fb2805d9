"use client";

import React, { createContext, useContext, useState, ReactNode } from "react";

interface ImagePreview {
  id: string;
  dataUrl: string;
  file: File;
}

interface CodeOCRContextType {
  images: ImagePreview[];
  setImages: React.Dispatch<React.SetStateAction<ImagePreview[]>>;
  isProcessing: boolean;
  setIsProcessing: React.Dispatch<React.SetStateAction<boolean>>;
  extractedCode: string | null;
  setExtractedCode: React.Dispatch<React.SetStateAction<string | null>>;
  error: string | null;
  setError: React.Dispatch<React.SetStateAction<string | null>>;
  language: string;
  setLanguage: React.Dispatch<React.SetStateAction<string>>;
  processImage: () => Promise<void>;
}

const CodeOCRContext = createContext<CodeOCRContextType | undefined>(undefined);

export function CodeOCRContextProvider({ children }: { children: ReactNode }) {
  const [images, setImages] = useState<ImagePreview[]>([]);
  const [isProcessing, setIsProcessing] = useState(false);
  const [extractedCode, setExtractedCode] = useState<string | null>(null);
  const [error, setError] = useState<string | null>(null);
  // Default language is auto-detect, but can be any valid language identifier
  const [language, setLanguage] = useState<string>("auto");

  const processImage = async () => {
    if (images.length === 0) {
      setError("Please upload an image first");
      return;
    }

    try {
      setIsProcessing(true);
      setError(null);

      // Get the first image
      const image = images[0];

      // Create form data
      const formData = new FormData();
      formData.append("file", image.file);

      // Add language if selected (not auto-detect)
      if (language && language !== "auto") {
        formData.append("language", language);
      }

      // Send request to the API
      const response = await fetch("/api/codeocr", {
        method: "POST",
        body: formData,
      });

      const result = await response.json();

      if (result.code === 0 && result.data) {
        setExtractedCode(result.data.extracted_code);
      } else {
        setError(result.message || "Error processing image");
      }
    } catch (err) {
      console.error("Error processing image:", err);
      setError("Error processing image");
    } finally {
      setIsProcessing(false);
    }
  };

  return (
    <CodeOCRContext.Provider
      value={{
        images,
        setImages,
        isProcessing,
        setIsProcessing,
        extractedCode,
        setExtractedCode,
        error,
        setError,
        language,
        setLanguage,
        processImage,
      }}
    >
      {children}
    </CodeOCRContext.Provider>
  );
}

export function useCodeOCRContext() {
  const context = useContext(CodeOCRContext);
  if (context === undefined) {
    throw new Error(
      "useCodeOCRContext must be used within a CodeOCRContextProvider"
    );
  }
  return context;
}
