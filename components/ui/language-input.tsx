"use client";

import React from "react";
import { Input } from "@/components/ui/input";
import { useTranslations } from "next-intl";
import { cn } from "@/lib/utils";

interface LanguageInputProps {
  value: string;
  onValueChange: (value: string) => void;
  className?: string;
  placeholder?: string;
}

export function LanguageInput({
  value,
  onValueChange,
  className,
  placeholder,
}: LanguageInputProps) {
  const t = useTranslations("language_selector");

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    onValueChange(e.target.value.toLowerCase() || "auto");
  };

  return (
    <div className={cn("relative flex items-center gap-2", className)}>
      <label className="text-sm font-medium whitespace-nowrap">{t("prompt")}</label>
      <Input
        value={value === "auto" ? "" : value}
        onChange={handleChange}
        placeholder={placeholder || t("auto_detect")}
        className="bg-[#f5f5f4] border-[#e7e5e4] text-[#78716c] h-9 rounded-md"
      />
    </div>
  );
}
