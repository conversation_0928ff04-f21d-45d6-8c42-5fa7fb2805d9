"use client";

import React, { useState, useRef, useCallback, useEffect } from "react";
import { cn } from "@/lib/utils";
import Image from "next/image";
import { IconPhoto, IconTrash } from "@tabler/icons-react";
import { useTranslations } from "next-intl";
import { PhotoProvider, PhotoView } from "react-photo-view";
import "react-photo-view/dist/react-photo-view.css";

interface ImagePreview {
  id: string;
  dataUrl: string;
  file: File;
}

interface ImageUploaderProps {
  className?: string;
  onImageUpload?: (file: File) => void;
  onImageChange?: (dataUrl: string) => void;
  onImagesChange?: (images: ImagePreview[]) => void;
  maxSizeMB?: number;
  acceptedTypes?: string[];
  defaultImage?: string;
}

export function ImageUploader({
  className,
  onImageUpload,
  onImageChange,
  onImagesChange,
  maxSizeMB = 5,
  acceptedTypes = ["image/jpeg", "image/png", "image/gif", "image/webp"],
  defaultImage,
}: ImageUploaderProps) {
  const t = useTranslations("image_uploader");

  const [images, setImages] = useState<ImagePreview[]>([]);
  const [isDragging, setIsDragging] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const dropAreaRef = useRef<HTMLDivElement>(null);
  const maxSizeBytes = maxSizeMB * 1024 * 1024;

  // Initialize with default image if provided
  useEffect(() => {
    if (defaultImage) {
      const img = document.createElement('img');
      img.onload = () => {
        // Create a dummy file object for the default image
        fetch(defaultImage)
          .then(res => res.blob())
          .then(blob => {
            const file = new File([blob], "default-image.jpg", { type: "image/jpeg" });
            const newImage: ImagePreview = {
              id: crypto.randomUUID(),
              dataUrl: defaultImage,
              file: file
            };
            setImages([newImage]);
            if (onImageChange) onImageChange(defaultImage);
            if (onImagesChange) onImagesChange([newImage]);
          });
      };
      img.src = defaultImage;
    }
  }, [defaultImage, onImageChange, onImagesChange]);

  // Delete an image by id
  const handleDeleteImage = useCallback((id: string) => {
    setImages(prevImages => {
      const newImages = prevImages.filter(img => img.id !== id);

      // Update external handlers
      if (newImages.length > 0) {
        if (onImageChange) onImageChange(newImages[0].dataUrl);
        if (onImagesChange) onImagesChange(newImages);
      } else {
        if (onImageChange) onImageChange("");
        if (onImagesChange) onImagesChange([]);
      }

      return newImages;
    });
  }, [onImageChange, onImagesChange]);

  // 处理文件上传
  const handleFileUpload = useCallback(
    (file: File) => {
      // 验证文件类型
      if (!acceptedTypes.includes(file.type)) {
        setError(t("file_type_error", {
          formats: acceptedTypes.map((type) => type.split("/")[1]).join(", ")
        }));
        return;
      }

      // 验证文件大小
      if (file.size > maxSizeBytes) {
        setError(t("file_size_error", {
          size: maxSizeMB
        }));
        return;
      }

      // 清除错误
      setError(null);

      // 创建文件预览
      const reader = new FileReader();
      reader.onload = (e) => {
        const dataUrl = e.target?.result as string;

        // Create a new image preview
        const newImage: ImagePreview = {
          id: crypto.randomUUID(),
          dataUrl,
          file
        };

        // Add to images array
        setImages(prevImages => {
          const updatedImages = [...prevImages, newImage];

          // Update external handlers
          if (onImageChange) onImageChange(dataUrl);
          if (onImagesChange) onImagesChange(updatedImages);

          return updatedImages;
        });
      };
      reader.readAsDataURL(file);

      // 调用外部回调
      if (onImageUpload) onImageUpload(file);
    },
    [acceptedTypes, maxSizeBytes, maxSizeMB, onImageChange, onImagesChange, onImageUpload, t]
  );

  // 处理点击上传
  const handleButtonClick = () => {
    fileInputRef.current?.click();
  };

  // 处理文件选择
  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    if (files && files.length > 0) {
      // Process all selected files
      Array.from(files).forEach(file => {
        handleFileUpload(file);
      });
    }
  };

  // 处理拖放事件
  const handleDragEnter = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragging(true);
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragging(true);
  };

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragging(false);
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragging(false);

    const file = e.dataTransfer.files?.[0];
    if (file) {
      handleFileUpload(file);
    }
  };

  // 处理剪贴板粘贴
  useEffect(() => {
    const handlePaste = (e: ClipboardEvent) => {
      const items = e.clipboardData?.items;
      if (!items) return;

      for (let i = 0; i < items.length; i++) {
        if (items[i].type.indexOf("image") !== -1) {
          const file = items[i].getAsFile();
          if (file) {
            handleFileUpload(file);
            break;
          }
        }
      }
    };

    // 添加全局粘贴事件监听
    document.addEventListener("paste", handlePaste);

    // 清理函数
    return () => {
      document.removeEventListener("paste", handlePaste);
    };
  }, [handleFileUpload]);

  return (
    <div
      className={cn(
        "flex flex-col items-center justify-center w-full",
        className
      )}
    >
      {/* Upload Area - Separated from preview */}
      <div
        ref={dropAreaRef}
        className={cn(
          "relative flex flex-col items-center justify-center w-full min-h-[200px] border border-dashed rounded-lg transition-all cursor-pointer p-6",
          isDragging
            ? "border-primary bg-primary/5"
            : "border-border bg-background/50 hover:bg-background/80"
        )}
        onClick={handleButtonClick}
        onDragEnter={handleDragEnter}
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
        onDrop={handleDrop}
      >
        <div className="flex flex-col items-center justify-center text-center w-full">
          <IconPhoto className="w-12 h-12 text-primary mb-4" />
          <p className="mb-2 text-base text-foreground">
            {t("drag_drop_paste")}
          </p>
          <p className="text-xs text-muted-foreground">
            {t("supported_format")}
          </p>
          <p className="mt-4 text-sm text-primary font-medium flex items-center">
            {t("premium_feature")}
          </p>

          <input
            ref={fileInputRef}
            type="file"
            multiple
            className="hidden"
            accept={acceptedTypes.join(",")}
            onChange={handleFileChange}
          />
        </div>
      </div>

      {/* Error message */}
      {error && <p className="mt-2 text-sm text-destructive">{error}</p>}

      {/* Thumbnails section - Now outside the drop area */}
      {images.length > 0 && (
        <div className="w-full mt-4">
          <PhotoProvider
            maskOpacity={0.8}
            speed={() => 300}
            loop={false}
            photoClosable={true}
          >
            <div className="flex flex-wrap gap-2 justify-start">
              {images.map((img) => (
                <div
                  key={img.id}
                  className="relative w-20 h-20 rounded-lg overflow-hidden border border-border"
                  onClick={(e) => e.stopPropagation()}
                >
                  <PhotoView
                    src={img.dataUrl}
                  >
                    <Image
                      src={img.dataUrl}
                      alt={t("uploaded_image")}
                      fill
                      className="object-cover cursor-zoom-in"
                    />
                  </PhotoView>
                  <button
                    type="button"
                    onClick={(e) => {
                      e.stopPropagation();
                      handleDeleteImage(img.id);
                    }}
                    className="absolute top-1 right-1 rounded-full p-1 transition-colors cursor-pointer hover:bg-red-100 dark:hover:bg-red-900/30 group"
                  >
                    <IconTrash className="w-3 h-3 text-primary group-hover:text-red-500 transition-colors" />
                  </button>
                </div>
              ))}
            </div>
          </PhotoProvider>
        </div>
      )}
    </div>
  );
}
