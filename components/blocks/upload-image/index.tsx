"use client";

import React from "react";
import { ImageUploader } from "@/components/ui/image-uploader";
import { Card, CardContent } from "@/components/ui/card";
import { Section as SectionType } from "@/types/blocks/section";
import { Button } from "@/components/ui/button";
import { Loader2 } from "lucide-react";
import { useTranslations } from "next-intl";
import { useCodeOCRContext } from "@/contexts/codeocr";
import { LanguageInput } from "@/components/ui/language-input";

export default function ImageUploadSection({
  section,
}: {
  section: SectionType;
}) {
  const t = useTranslations("image_upload_section");
  const {
    images,
    setImages,
    isProcessing,
    processImage,
    language,
    setLanguage,
  } = useCodeOCRContext();

  if (section?.disabled) {
    return null;
  }

  const handleImagesChange = (newImages: any[]) => {
    setImages(newImages);
  };

  return (
    <section id={section?.name || "image-upload"} className="pt-2 pb-2">
      <div className="container">
        <div className="max-w-3xl mx-auto">
          <Card>
            <CardContent>
              {/* Image Upload Component */}
              <ImageUploader
                onImagesChange={handleImagesChange}
                maxSizeMB={5}
                acceptedTypes={[
                  "image/jpeg",
                  "image/png",
                  "image/gif",
                  "image/webp",
                ]}
              />

              {/* Language Input */}
              <div className="mt-4">
                <LanguageInput
                  value={language}
                  onValueChange={setLanguage}
                  className="w-full"
                />
              </div>

              {/* Extract Code Button - Full Width */}
              <div className="mt-4">
                <Button
                  onClick={processImage}
                  disabled={isProcessing || images.length === 0}
                  className="w-full whitespace-nowrap cursor-pointer"
                  variant="default"
                >
                  {isProcessing ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      {t("processing")}
                    </>
                  ) : (
                    t("extract_code")
                  )}
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </section>
  );
}
