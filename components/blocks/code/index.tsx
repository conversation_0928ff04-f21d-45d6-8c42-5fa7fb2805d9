"use client";

import React from "react";
import { Card, CardContent } from "@/components/ui/card";
import { Section as SectionType } from "@/types/blocks/section";
import { Prism as SyntaxHighlighter } from "react-syntax-highlighter";
import { vscDarkPlus } from "react-syntax-highlighter/dist/esm/styles/prism";
import { useCodeOCRContext } from "@/contexts/codeocr";
import { useTranslations } from "next-intl";

export default function Code({ section }: { section: SectionType }) {
  const t = useTranslations("code_section");
  const { extractedCode, isProcessing, error, language } = useCodeOCRContext();

  if (section?.disabled) {
    return null;
  }

  return (
    <section id={section?.name || "code"} className="pt-2 pb-8">
      <div className="container">
        <div className="max-w-3xl mx-auto">
          <Card>
            <CardContent className="p-6">
              {isProcessing ? (
                <div className="flex justify-center items-center py-8">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
                </div>
              ) : error ? (
                <div className="p-4 bg-destructive/10 text-destructive rounded-md">
                  {error}
                </div>
              ) : extractedCode ? (
                <div className="rounded-md overflow-hidden">
                  <SyntaxHighlighter
                    language={language === "auto" ? undefined : language}
                    style={vscDarkPlus}
                    showLineNumbers
                    customStyle={{
                      margin: 0,
                      borderRadius: "0.375rem",
                      fontSize: "0.9rem",
                    }}
                  >
                    {extractedCode}
                  </SyntaxHighlighter>
                </div>
              ) : (
                <div className="text-center py-8 text-muted-foreground">
                  {t("no_code_yet")}
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      </div>
    </section>
  );
}
