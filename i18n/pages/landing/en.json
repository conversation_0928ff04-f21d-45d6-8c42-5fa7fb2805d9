{"template": "codeocr", "theme": "light", "header": {"brand": {"title": "CodeOCR", "logo": {"src": "/logo.png", "alt": "CodeOCR"}, "url": "/"}, "nav": {"items": [{"title": "Pricing", "url": "/pricing", "icon": "RiMoneyDollarCircleLine"}, {"title": "Showcase", "url": "/#showcase", "icon": "RiApps2Line"}, {"title": "Blog", "url": "/posts", "icon": "RiArticleLine"}]}, "buttons": [{"title": "Open Source", "url": "https://fengchao.pro", "target": "_blank", "variant": "link", "icon": "RiArrowRightUpLine"}], "show_sign": true, "show_theme": true, "show_locale": true}, "hero": {"title": "Code Screenshot to Editable Code", "highlight_text": "Editable Code", "description": "Easily convert your screenshots into fully editable code snippets. Powered by Gemini."}, "codeOCR": {"name": "codeocr"}, "branding": {"disabled": false, "title": "For Any Programming Language", "items": [{"title": "C++", "image": {"src": "/imgs/logos/c-plusplus.svg", "alt": "C++"}}, {"title": "Python", "image": {"src": "/imgs/logos/python.svg", "alt": "Python"}}, {"title": "TypeScript", "image": {"src": "/imgs/logos/typescript.svg", "alt": "TypeScript"}}, {"title": "Rust", "image": {"src": "/imgs/logos/rust.svg", "alt": "Rust"}}]}, "footer": {"name": "footer", "brand": {"title": "CodeOCR", "description": "Get editable code from screenshots.", "logo": {"src": "/logo.png", "alt": "CodeOCR"}, "url": "/"}, "copyright": "© 2025 • CodeOCR All rights reserved.", "nav": {"items": [{"title": "About", "children": [{"title": "Pricing", "url": "/pricing", "target": "_self"}, {"title": "Showcases", "url": "/#showcase", "target": "_self"}]}]}, "social": {"items": [{"title": "<PERSON><PERSON><PERSON>", "icon": "RiGithubFill", "url": "https://github.com/jeremy-feng", "target": "_blank"}, {"title": "Email", "icon": "RiMailLine", "url": "mailto:<EMAIL>", "target": "_self"}]}, "agreement": {"items": [{"title": "Privacy Policy", "url": "/privacy-policy"}, {"title": "Terms of Service", "url": "/terms-of-service"}]}}}