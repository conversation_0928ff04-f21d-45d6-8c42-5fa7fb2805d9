{"template": "codeocr", "theme": "light", "header": {"brand": {"title": "CodeOCR", "logo": {"src": "/logo.png", "alt": "CodeOCR"}, "url": "/"}, "nav": {"items": [{"title": "定价", "url": "/pricing", "icon": "RiMoneyDollarCircleLine"}, {"title": "案例展示", "url": "/#showcase", "icon": "RiApps2Line"}, {"title": "博客", "url": "/posts", "icon": "RiArticleLine"}]}, "buttons": [{"title": "开源项目", "url": "https://fengchao.pro", "target": "_blank", "variant": "link", "icon": "RiArrowRightUpLine"}], "show_sign": true, "show_theme": true, "show_locale": true}, "hero": {"title": "将截图转换为可编辑代码", "highlight_text": "可编辑代码", "description": "轻松将代码截图转换为完全可编辑的代码片段。由 Gemini 提供支持。"}, "branding": {"disabled": false, "title": "支持各种编程语言", "items": [{"title": "C++", "image": {"src": "/imgs/logos/c-plusplus.svg", "alt": "C++"}}, {"title": "Python", "image": {"src": "/imgs/logos/python.svg", "alt": "Python"}}, {"title": "TypeScript", "image": {"src": "/imgs/logos/typescript.svg", "alt": "TypeScript"}}, {"title": "Rust", "image": {"src": "/imgs/logos/rust.svg", "alt": "Rust"}}]}, "codeOCR": {"name": "codeocr"}, "footer": {"name": "footer", "brand": {"title": "CodeOCR", "description": "从截图中获取可编辑代码。", "logo": {"src": "/logo.png", "alt": "CodeOCR"}, "url": "/"}, "copyright": "© 2025 • CodeOCR 保留所有权利。", "nav": {"items": [{"title": "关于", "children": [{"title": "定价", "url": "/pricing", "target": "_self"}, {"title": "案例展示", "url": "/#showcase", "target": "_self"}]}]}, "social": {"items": [{"title": "<PERSON><PERSON><PERSON>", "icon": "RiGithubFill", "url": "https://github.com/jeremy-feng", "target": "_blank"}, {"title": "邮箱", "icon": "RiMailLine", "url": "mailto:<EMAIL>", "target": "_self"}]}, "agreement": {"items": [{"title": "隐私政策", "url": "/privacy-policy"}, {"title": "服务条款", "url": "/terms-of-service"}]}}}